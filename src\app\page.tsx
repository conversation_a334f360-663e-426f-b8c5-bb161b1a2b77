'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useAuth } from '@/contexts/AuthContext';

export default function Home() {
  const router = useRouter();
  const { user, loading } = useAuth();

  // Redirect authenticated users to dashboard
  useEffect(() => {
    if (!loading && user) {
      router.push('/dashboard');
    }
  }, [user, loading, router]);

  // Show loading state while checking auth
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center mx-auto mb-4 animate-pulse border border-white/30">
            <span className="text-white font-bold text-xl">
              🔗
            </span>
          </div>
          <p className="text-white/80 text-lg">Učitavanje...</p>
        </div>
      </div>
    );
  }

  // Only show homepage to non-authenticated users
  if (user) {
    return null; // Will redirect in useEffect
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-black/10"></div>
      <div className="absolute top-20 left-10 w-72 h-72 bg-white/5 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute bottom-20 right-10 w-96 h-96 bg-white/5 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-white/3 rounded-full blur-3xl"></div>

      {/* Header */}
      <header className="relative z-10 border-b border-white/20 backdrop-blur-sm bg-white/10">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center border border-white/30">
              <span className="text-white font-bold text-lg">
                🔗
              </span>
            </div>
            <span className="text-xl font-bold text-white">
              INFLUEXUS
            </span>
          </div>
          <div className="flex items-center space-x-4">
            <Button variant="ghost" className="text-white hover:bg-white/20 border-white/30" asChild>
              <Link href="/prijava">Prijava</Link>
            </Button>
            <Button className="bg-white text-instagram-purple hover:bg-white/90 font-semibold" asChild>
              <Link href="/registracija">Registracija</Link>
            </Button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative z-10 py-20 px-4">
        <div className="container mx-auto text-center max-w-4xl">
          <div className="mb-8">
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6 leading-tight">
              Povezujemo kreatore sa{' '}
              <span className="bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">
                brendovima
              </span>
            </h1>
            <p className="text-xl text-white/80 mb-8 max-w-2xl mx-auto leading-relaxed">
              Prva bosanska platforma za influencer marketing. Transparentno,
              sigurno i efikasno.
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Button
              size="lg"
              className="text-lg px-8 py-6 bg-white/90 text-purple-700 hover:bg-white font-semibold shadow-lg hover:shadow-xl transition-all duration-300 backdrop-blur-sm"
              asChild
            >
              <Link href="/registracija">
                <span className="mr-2">✨</span>
                Počni kao influencer
              </Link>
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="text-lg px-8 py-6 border-white/40 text-white hover:bg-white/20 backdrop-blur-sm font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
              asChild
            >
              <Link href="/registracija">
                <span className="mr-2">🚀</span>
                Kreiraj kampanju
              </Link>
            </Button>
          </div>

          {/* Stats or features preview */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16">
            <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6 text-center hover:bg-white/15 transition-all duration-300">
              <div className="text-3xl font-bold text-white mb-2">500+</div>
              <div className="text-white/80">Aktivnih influencera</div>
            </div>
            <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6 text-center hover:bg-white/15 transition-all duration-300">
              <div className="text-3xl font-bold text-white mb-2">100+</div>
              <div className="text-white/80">Uspešnih kampanja</div>
            </div>
            <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6 text-center hover:bg-white/15 transition-all duration-300">
              <div className="text-3xl font-bold text-white mb-2">50+</div>
              <div className="text-white/80">Partnera</div>
            </div>
          </div>
        </div>
      </section>

      {/* How it works */}
      <section className="relative py-20 px-4">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-center mb-12 text-white">
            Kako funkcioniše
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6 text-center hover:bg-white/15 transition-all duration-300">
              <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">👤</span>
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">1. Registruj se</h3>
              <p className="text-white/80">
                Kreiraj profil kao influencer ili biznis. Dodaj svoje
                informacije i preferencije.
              </p>
            </div>

            <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6 text-center hover:bg-white/15 transition-all duration-300">
              <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🤝</span>
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">2. Pronađi saradnju</h3>
              <p className="text-white/80">
                Influenceri pronalaze kampanje, biznisi biraju kreatore. Sve
                transparentno i sigurno.
              </p>
            </div>

            <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-6 text-center hover:bg-white/15 transition-all duration-300">
              <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">💰</span>
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">3. Zaradi</h3>
              <p className="text-white/80">
                Kreiraj sadržaj, ispuni ugovor i automatski primi plaćanje.
                Jednostavno i sigurno.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* For Influencers */}
      <section className="relative py-20 px-4">
        <div className="container mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold mb-6 text-white">Za influencere</h2>
              <ul className="space-y-4 text-lg">
                <li className="flex items-center space-x-3">
                  <span className="text-green-400">✓</span>
                  <span className="text-white/90">Pronađi kampanje koje odgovaraju tvojoj niši</span>
                </li>
                <li className="flex items-center space-x-3">
                  <span className="text-green-400">✓</span>
                  <span className="text-white/90">Postavi svoje cijene i uslove</span>
                </li>
                <li className="flex items-center space-x-3">
                  <span className="text-green-400">✓</span>
                  <span className="text-white/90">Sigurno plaćanje kroz escrow sistem</span>
                </li>
                <li className="flex items-center space-x-3">
                  <span className="text-green-400">✓</span>
                  <span className="text-white/90">Izgradi svoj portfolio i reputaciju</span>
                </li>
              </ul>
              <Button className="mt-8 bg-white/90 text-purple-700 hover:bg-white font-semibold" size="lg" asChild>
                <Link href="/registracija">Registruj se kao influencer</Link>
              </Button>
            </div>
            <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-8 text-center hover:bg-white/15 transition-all duration-300">
              <span className="text-6xl">📱</span>
              <p className="text-white/80 mt-4 text-lg">
                Kreiraj sadržaj koji voliš i zarađuj
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* For Businesses */}
      <section className="relative py-20 px-4">
        <div className="container mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-8 text-center hover:bg-white/15 transition-all duration-300">
              <span className="text-6xl">🏢</span>
              <p className="text-white/80 mt-4 text-lg">
                Dosegni svoju ciljanu publiku
              </p>
            </div>
            <div>
              <h2 className="text-3xl font-bold mb-6 text-white">Za biznise</h2>
              <ul className="space-y-4 text-lg">
                <li className="flex items-center space-x-3">
                  <span className="text-green-400">✓</span>
                  <span className="text-white/90">Kreiraj kampanje za svaki budžet</span>
                </li>
                <li className="flex items-center space-x-3">
                  <span className="text-green-400">✓</span>
                  <span className="text-white/90">Biraj između stotina lokalnih influencera</span>
                </li>
                <li className="flex items-center space-x-3">
                  <span className="text-green-400">✓</span>
                  <span className="text-white/90">Transparentno praćenje rezultata</span>
                </li>
                <li className="flex items-center space-x-3">
                  <span className="text-green-400">✓</span>
                  <span className="text-white/90">Plaćaj samo za uspješno izvršene kampanje</span>
                </li>
              </ul>
              <Button className="mt-8 bg-white/90 text-purple-700 hover:bg-white font-semibold" size="lg" asChild>
                <Link href="/registracija">Kreiraj prvu kampanju</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="relative border-t border-white/20 py-12 px-4">
        <div className="container mx-auto text-center">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <div className="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center border border-white/30">
              <span className="text-white font-bold text-lg">
                🔗
              </span>
            </div>
            <span className="text-xl font-bold text-white">INFLUEXUS</span>
          </div>
          <p className="text-white/80 mb-4">
            Povezujemo. Kreiramo. Uspijevamo.
          </p>
          <p className="text-sm text-white/60">
            © 2025 INFLUEXUS. Sva prava zadržana.
          </p>
        </div>
      </footer>
    </div>
  );
}
