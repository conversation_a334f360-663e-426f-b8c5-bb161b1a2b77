'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { 
  LayoutDashboard, 
  FileText, 
  Inbox, 
  Users, 
  MessageCircle, 
  ChevronDown,
  User,
  Settings,
  DollarSign,
  CheckCircle,
  Send,
  LogOut
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { NotificationDropdown } from '@/components/notifications/NotificationDropdown';
import { useAuth } from '@/contexts/AuthContext';
import { cn } from '@/lib/utils';

interface DesktopHeaderProps {
  userType: 'influencer' | 'business';
  profile: any;
}

interface NavItem {
  name: string;
  href: string;
  icon: any;
}

interface ProfileMenuItem {
  name: string;
  href: string;
  icon: any;
  description?: string;
}

export function DesktopHeader({ userType, profile }: DesktopHeaderProps) {
  const pathname = usePathname();
  const { signOut } = useAuth();

  // Main navigation items for business users
  const businessNavigation: NavItem[] = [
    { name: 'Dashboard', href: '/dashboard/biznis', icon: LayoutDashboard },
    { name: 'Kampanje', href: '/dashboard/campaigns', icon: FileText },
    { name: 'Aplikacije', href: '/dashboard/biznis/applications', icon: Inbox },
    { name: 'Influenceri', href: '/marketplace/influencers', icon: Users },
    { name: 'Poruke', href: '/dashboard/chat', icon: MessageCircle },
  ];

  // Main navigation items for influencer users
  const influencerNavigation: NavItem[] = [
    { name: 'Dashboard', href: '/dashboard/influencer', icon: LayoutDashboard },
    { name: 'Kampanje', href: '/marketplace/campaigns', icon: FileText },
    { name: 'Ponude', href: '/dashboard/influencer/offers', icon: Inbox },
    { name: 'Poruke', href: '/dashboard/chat', icon: MessageCircle },
  ];

  // Profile dropdown items for business users
  const businessProfileItems: ProfileMenuItem[] = [
    {
      name: 'Moj račun',
      href: '/dashboard/biznis/account',
      icon: User,
      description: 'Podaci o firmi i sigurnost',
    },
    {
      name: 'Postavke profila',
      href: '/dashboard/biznis/profile',
      icon: Settings,
      description: 'Javni profil firme',
    },
    {
      name: 'Moje ponude',
      href: '/dashboard/biznis/offers',
      icon: Send,
      description: 'Direktne ponude influencerima',
    },
    {
      name: 'Završeni poslovi',
      href: '/dashboard/biznis/zavrseni-poslovi',
      icon: CheckCircle,
      description: 'Praćenje završenih poslova',
    },
  ];

  // Profile dropdown items for influencer users
  const influencerProfileItems: ProfileMenuItem[] = [
    {
      name: 'Moj račun',
      href: '/dashboard/influencer/account',
      icon: User,
      description: 'Lični podaci i sigurnost',
    },
    {
      name: 'Postavke profila',
      href: '/dashboard/influencer/profile',
      icon: Settings,
      description: 'Javni profil i cijene',
    },
    {
      name: 'Moje aplikacije',
      href: '/dashboard/influencer/applications',
      icon: FileText,
      description: 'Aplikacije na kampanje',
    },
    {
      name: 'Zarada',
      href: '/dashboard/influencer/earnings',
      icon: DollarSign,
      description: 'Pregled zarade i isplate',
    },
    {
      name: 'Završeni poslovi',
      href: '/dashboard/influencer/zavrseni-poslovi',
      icon: CheckCircle,
      description: 'Praćenje završenih poslova',
    },
  ];

  const navigation = userType === 'business' ? businessNavigation : influencerNavigation;
  const profileItems = userType === 'business' ? businessProfileItems : influencerProfileItems;

  const isActive = (href: string) => {
    if (href === '/dashboard/influencer' || href === '/dashboard/biznis') {
      return pathname === href;
    }
    return pathname.startsWith(href);
  };

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between px-6">
        {/* Logo */}
        <div className="flex items-center space-x-8">
          <Link href="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">🔗</span>
            </div>
            <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              InfluConnect
            </h1>
          </Link>

          {/* Navigation - Hidden on mobile */}
          <nav className="hidden md:flex items-center space-x-1">
            {navigation.map((item) => (
              <Link key={item.name} href={item.href}>
                <Button
                  variant="ghost"
                  className={cn(
                    'flex items-center space-x-2 px-3 py-2 text-sm font-medium transition-colors',
                    isActive(item.href)
                      ? 'text-primary bg-primary/10'
                      : 'text-muted-foreground hover:text-foreground hover:bg-accent/50'
                  )}
                >
                  <item.icon className="h-4 w-4" />
                  <span>{item.name}</span>
                </Button>
              </Link>
            ))}
          </nav>
        </div>

        {/* Right side - Notifications & Profile */}
        <div className="flex items-center space-x-4">
          {/* Notifications */}
          <NotificationDropdown />

          {/* Profile */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="flex items-center space-x-2 px-2 py-1">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={profile?.avatar_url} alt="Profile" />
                  <AvatarFallback>
                    {profile?.full_name?.charAt(0) || profile?.username?.charAt(0) || 'U'}
                  </AvatarFallback>
                </Avatar>
                <ChevronDown className="h-4 w-4 text-muted-foreground" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <div className="flex items-center space-x-2 p-2">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={profile?.avatar_url} alt="Profile" />
                  <AvatarFallback>
                    {profile?.full_name?.charAt(0) || profile?.username?.charAt(0) || 'U'}
                  </AvatarFallback>
                </Avatar>
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium">
                    {profile?.full_name || profile?.username}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    @{profile?.username}
                  </p>
                </div>
              </div>
              <DropdownMenuSeparator />
              {profileItems.map((item) => (
                <DropdownMenuItem key={item.name} asChild className="cursor-pointer">
                  <Link href={item.href}>
                    <item.icon className="mr-2 h-4 w-4" />
                    <span>{item.name}</span>
                  </Link>
                </DropdownMenuItem>
              ))}
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleSignOut} className="cursor-pointer text-red-600">
                <LogOut className="mr-2 h-4 w-4" />
                <span>Odjavi se</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
